document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("mobile-menu-button"),g=document.getElementById("mobile-menu");o&&g&&o.addEventListener("click",()=>{g.classList.toggle("hidden")});const l=document.getElementById("headerSearchContainer"),d=document.getElementById("mobileHeaderSearchContainer");let h=!1;function y(){const e=window.scrollY;l&&(e<=50?(l.classList.add("opacity-0","-translate-y-2"),l.classList.remove("opacity-100","translate-y-0")):(l.classList.remove("opacity-0","-translate-y-2"),l.classList.add("opacity-100","translate-y-0"))),d&&(e<=50?(d.classList.add("opacity-0","-translate-y-2"),d.classList.remove("opacity-100","translate-y-0")):(d.classList.remove("opacity-0","-translate-y-2"),d.classList.add("opacity-100","translate-y-0"))),h=!1}function w(){h||(requestAnimationFrame(y),h=!0)}window.addEventListener("scroll",w,{passive:!0}),y();const i=document.getElementById("productSearch"),u=document.getElementById("mobileProductSearch"),s=document.getElementById("searchResults");let f;function m(){return window.innerWidth<768}function v(e){if(m()){e.blur();const t=e.value.trim();window.openSearchModal?.(t)}}function x(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(n=>n!==e);c.unshift(e);const r=c.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(r))}catch(t){console.error("Failed to save recent search:",t)}}function p(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&s){const t=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((r,n)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${n<e.length-1?"border-b border-primary-100":""}">
                  <button class="recent-search-item flex-1 text-left" data-query="${r}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${r}</span>
                    </div>
                  </button>
                  <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${r}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>
          `;s.innerHTML=t,s.classList.remove("hidden"),s.querySelectorAll(".recent-search-item").forEach(r=>{r.addEventListener("click",()=>{const n=r.getAttribute("data-query");n&&i&&(i.value=n,L(n))})}),s.querySelectorAll(".delete-recent-search").forEach(r=>{r.addEventListener("click",n=>{n.stopPropagation();const a=r.getAttribute("data-query");a&&(S(a),p())})}),s.querySelector("#clearRecentSearches")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),s?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}function S(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(r=>r!==e);localStorage.setItem("recentSearches",JSON.stringify(c))}catch(t){console.error("Failed to remove recent search:",t)}}function L(e){x(e),b({value:e})}async function b(e){const t=e.value.trim();f&&clearTimeout(f),t.length>2?s&&(s.classList.remove("hidden"),s.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <img src="/logo.svg" alt="InfPik" class="w-4 h-4 animate-pulse" />
                <span class="text-sm">Searching products for "${t}"...</span>
              </div>
            </div>
          `,f=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(t)}`)).json();if(s&&!s.classList.contains("hidden"))if(r.results&&r.results.length>0){const n=r.results.map(a=>`
                    <button onclick="window.location.href='${a.url||"#"}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                            ${a.image?`<img src="${a.image}" alt="${a.name||"Product"}" class="w-full h-full object-cover">`:`
                              <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                                <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                            `}
                          </div>
                          <div class="flex-1 min-w-0">
                            <div class="text-primary-900 font-medium truncate">${a.name||"Unknown Product"}</div>
                            <div class="text-sm text-accent-600 font-medium">$${a.price||"0"} ${a.currency||"USD"}</div>
                          </div>
                        </div>
                        <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </button>
                  `).join("");s.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Search results for "${t}"
                      </div>
                      ${n}
                      ${r.total>r.results.length?`
                        <div class="p-3 border-t border-primary-100">
                          <div class="text-center text-primary-600 text-sm">
                            Showing ${r.results.length} of ${r.total} products
                          </div>
                        </div>
                      `:""}
                    </div>
                  `}else s.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No products found for "${t}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(c){console.error("Tag search error:",c),s&&!s.classList.contains("hidden")&&(s.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):p()}i&&(i.addEventListener("focus",e=>{v(e.target)}),i.addEventListener("click",e=>{v(e.target)}),i.addEventListener("input",e=>{m()||b(e.target)}),i.addEventListener("focus",e=>{!m()&&!e.target.value.trim()&&p()}),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&(m()?window.openSearchModal?.(t):(x(t),window.location.href=`/products?search=${encodeURIComponent(t)}`))}})),u&&(u.addEventListener("focus",e=>{v(e.target)}),u.addEventListener("click",e=>{v(e.target)}),u.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=e.target.value.trim();t&&window.openSearchModal?.(t)}})),document.addEventListener("click",e=>{s&&!i?.contains(e.target)&&!s.contains(e.target)&&s.classList.add("hidden")})});"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(o=>{console.log("SW registered: ",o)}).catch(o=>{console.log("SW registration failed: ",o)})});
