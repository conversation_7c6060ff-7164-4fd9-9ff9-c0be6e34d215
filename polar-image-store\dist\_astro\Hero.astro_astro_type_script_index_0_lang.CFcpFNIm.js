document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("categoryScroll");if(o){let e=!1,t,c;o.addEventListener("touchstart",a=>{e=!0,t=a.touches[0].pageX-o.offsetLeft,c=o.scrollLeft}),o.addEventListener("touchend",()=>{e=!1}),o.addEventListener("touchmove",a=>{if(!e)return;const l=(a.touches[0].pageX-o.offsetLeft-t)*2;o.scrollLeft=c-l}),o.addEventListener("mousedown",a=>{e=!0,t=a.pageX-o.offsetLeft,c=o.scrollLeft,o.style.cursor="grabbing"}),o.addEventListener("mouseleave",()=>{e=!1,o.style.cursor="grab"}),o.addEventListener("mouseup",()=>{e=!1,o.style.cursor="grab"}),o.addEventListener("mousemove",a=>{if(!e)return;a.preventDefault();const l=(a.pageX-o.offsetLeft-t)*2;o.scrollLeft=c-l}),o.style.cursor="grab";const n=document.querySelectorAll(".category-tab");n.forEach(a=>{a.addEventListener("click",d=>{const l=d.currentTarget.dataset.category;n.forEach(y=>{y.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),y.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),a.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),a.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),window.location.pathname==="/"?(console.log("📡 Dispatching categoryChange event for homepage:",l),window.dispatchEvent(new CustomEvent("categoryChange",{detail:{categoryId:l}}))):l==="all"?window.location.href="/products":window.location.href=`/products/category/${l}`})});const r=document.querySelectorAll(".tag-tab");r.forEach(a=>{a.addEventListener("click",d=>{const l=d.currentTarget.dataset.tag;r.forEach(m=>{m.classList.remove("bg-accent-500","text-white","border-accent-500","shadow-md"),m.classList.add("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500")}),d.currentTarget.classList.remove("bg-white","text-primary-900","border-primary-200","shadow-sm","hover:bg-primary-50","hover:border-accent-500"),d.currentTarget.classList.add("bg-accent-500","text-white","border-accent-500","shadow-md"),setTimeout(()=>{l==="all"?window.location.href="/products":window.location.href=`/products/tag/${l}`},150)})})}const i=document.getElementById("heroSearchInput"),s=document.getElementById("heroSearchResults");let f;function h(){return window.innerWidth<768}function v(e){if(h()){e.blur();const t=e.value.trim();window.openSearchModal?.(t)}}function g(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(r=>r!==e);c.unshift(e);const n=c.slice(0,10);localStorage.setItem("recentSearches",JSON.stringify(n))}catch(t){console.error("Failed to save recent search:",t)}}function w(e){try{const c=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(n=>n!==e);localStorage.setItem("recentSearches",JSON.stringify(c))}catch(t){console.error("Failed to remove recent search:",t)}}function u(){try{const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length>0&&s){const t=`
            <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
              <div class="flex items-center justify-between">
                <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
                <button id="heroSearchClearRecent" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
                  Clear
                </button>
              </div>
            </div>
            <div class="p-2">
              ${e.map((n,r)=>`
                <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors ${r<e.length-1?"border-b border-primary-100":""}">
                  <button class="hero-recent-search-item flex-1 text-left" data-query="${n}">
                    <div class="flex items-center gap-3">
                      <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span class="text-primary-900 text-sm">${n}</span>
                    </div>
                  </button>
                  <button class="hero-delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${n}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              `).join("")}
            </div>
          `;s.innerHTML=t,s.classList.remove("hidden"),s.querySelectorAll(".hero-recent-search-item").forEach(n=>{n.addEventListener("click",()=>{const r=n.getAttribute("data-query");r&&i&&(i.value=r,p(r))})}),s.querySelectorAll(".hero-delete-recent-search").forEach(n=>{n.addEventListener("click",r=>{r.stopPropagation();const a=n.getAttribute("data-query");a&&(w(a),u())})}),s.querySelector("#heroSearchClearRecent")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),s?.classList.add("hidden")})}}catch(e){console.error("Failed to load recent searches:",e)}}async function p(e){if(!(!e.trim()||!s))try{s.innerHTML='<div class="p-4 text-center text-primary-500">Searching...</div>',s.classList.remove("hidden");const c=await(await fetch(`/api/search?q=${encodeURIComponent(e)}`)).json();c.results&&c.results.length>0?(g(e),b(c.results,e)):x(e)}catch(t){console.error("Search error:",t),s.innerHTML='<div class="p-4 text-center text-red-500">Search failed. Please try again.</div>'}}function b(e,t){if(!s)return;console.log("Displaying results:",e);const c=e.map(r=>`
        <button class="hero-search-result-item w-full p-3 hover:bg-primary-50 transition-colors text-left border-b border-primary-100 last:border-b-0" data-url="${r.url||"#"}">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-10 h-10 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                ${r.image?`<img src="${r.image}" alt="${r.name||"Product"}" class="w-full h-full object-cover">`:`
                  <div class="w-full h-full bg-accent-100 flex items-center justify-center">
                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                `}
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium text-primary-900 truncate">${r.name||"Unknown Product"}</div>
                <div class="text-sm text-accent-600 font-medium">$${r.price||"0"} ${r.currency||"USD"}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join(""),n=`
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <span class="text-xs text-primary-500 font-medium">Search results for "${t}"</span>
        </div>
        ${c}
        ${e.length>=10?'<div class="p-3 text-center text-sm text-primary-500">Showing first 10 results</div>':""}
      `;s.innerHTML=n,s.classList.remove("hidden"),s.querySelectorAll(".hero-search-result-item").forEach(r=>{r.addEventListener("click",()=>{const a=r.getAttribute("data-url");a&&(window.location.href=a)})})}function x(e){s&&(s.innerHTML=`
        <div class="p-4 text-center">
          <div class="text-primary-500 mb-2">No icons found for "${e}"</div>
          <a href="/products" class="text-accent-600 hover:text-accent-700 text-sm font-medium">Browse all icons →</a>
        </div>
      `,s.classList.remove("hidden"))}function L(){const e=i?.value.trim();e?(clearTimeout(f),f=setTimeout(()=>{p(e)},300)):u()}i&&(i.addEventListener("focus",e=>{h()?v(e.target):i.value.trim()||u()}),i.addEventListener("click",e=>{h()&&v(e.target)}),i.addEventListener("input",L),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const t=i.value.trim();t&&(h()?window.openSearchModal?.(t):(g(t),window.location.href=`/products?search=${encodeURIComponent(t)}`))}})),document.addEventListener("click",e=>{s&&i&&!s.contains(e.target)&&!i.contains(e.target)&&s.classList.add("hidden")})});
