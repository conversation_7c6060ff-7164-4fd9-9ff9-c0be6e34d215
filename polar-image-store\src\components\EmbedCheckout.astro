---
// EmbedCheckout Component - Handle Polar embed checkout functionality
export interface Props {
  productId: string;
  variant?: 'button' | 'link';
  className?: string;
  children?: any;
  theme?: 'light' | 'dark';
  size?: 'sm' | 'md' | 'lg';
}

const { 
  productId,
  variant = 'button',
  className = '',
  theme = 'light',
  size = 'md'
} = Astro.props;

// Generate unique ID for this checkout trigger
const triggerId = `checkout-trigger-${productId}-${Math.random().toString(36).substr(2, 9)}`;

// Size classes
const sizeClasses = {
  sm: 'px-4 py-2 text-sm',
  md: 'px-6 py-3 text-base',
  lg: 'px-8 py-4 text-lg'
};

// Button variant classes
const buttonClasses = `
  inline-flex items-center justify-center gap-2 
  bg-accent-600 text-white rounded-full font-semibold 
  transition-all duration-300 
  hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5
  ${sizeClasses[size]}
  ${className}
`;

// Link variant classes  
const linkClasses = `
  inline-flex items-center gap-2 
  text-accent-600 hover:text-accent-700 font-medium 
  transition-colors duration-200
  ${className}
`;
---

{variant === 'button' ? (
  <button
    id={triggerId}
    class={buttonClasses}
    data-polar-checkout
    data-polar-checkout-theme={theme}
    data-product-id={productId}
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path>
    </svg>
    <slot>Buy Now</slot>
  </button>
) : (
  <a
    id={triggerId}
    href="#"
    class={linkClasses}
    data-polar-checkout
    data-polar-checkout-theme={theme}
    data-product-id={productId}
  >
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path>
    </svg>
    <slot>Buy Now</slot>
  </a>
)}

<!-- Loading state overlay -->
<div id={`${triggerId}-loading`} class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
  <div class="bg-white rounded-2xl p-8 flex flex-col items-center gap-4 shadow-2xl">
    <img src="/logo.svg" alt="InfPik" class="w-16 h-16 animate-pulse" />
    <span class="text-gray-700 font-medium">Loading checkout...</span>
  </div>
</div>

<!-- Include the Polar embed script -->
<script src="https://cdn.jsdelivr.net/npm/@polar-sh/checkout@0.1/dist/embed.global.js" defer></script>

<script>
  import { PolarEmbedCheckout } from '@polar-sh/checkout/embed';

  class EmbedCheckoutHandler {
    constructor() {
      this.init();
    }

    async init() {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.setupCheckout());
      } else {
        this.setupCheckout();
      }
    }

    async setupCheckout() {
      try {
        // Initialize Polar embed checkout
        PolarEmbedCheckout.init();
        
        // Add custom event listeners to all checkout triggers
        const triggers = document.querySelectorAll('[data-polar-checkout]');
        
        triggers.forEach(trigger => {
          trigger.addEventListener('click', async (e) => {
            e.preventDefault();
            
            const productId = trigger.getAttribute('data-product-id');
            const theme = trigger.getAttribute('data-polar-checkout-theme') || 'light';
            const triggerId = trigger.id;
            
            if (!productId) {
              console.error('Product ID not found for checkout trigger');
              return;
            }

            try {
              // Show loading state
              this.showLoading(triggerId);
              
              // Create checkout URL
              const checkoutUrl = await this.createCheckoutUrl(productId);
              
              if (!checkoutUrl) {
                throw new Error('Failed to create checkout URL');
              }

              // Create embed checkout (await resolves when iframe is fully loaded)
              const checkout = await PolarEmbedCheckout.create(checkoutUrl, theme);

              // Now that checkout is loaded, hide our loading overlay to avoid Polar's built-in spinner showing/sticking
              this.hideLoading(triggerId);

              // Setup event listeners
              this.setupCheckoutEvents(checkout, triggerId);

            } catch (error) {
              console.error('Checkout error:', error);
              this.hideLoading(triggerId);
              this.showError('Failed to open checkout. Please try again.');
            }
          });
        });
        
      } catch (error) {
        console.error('Failed to initialize embed checkout:', error);
      }
    }

    async createCheckoutUrl(productId) {
      try {
        const response = await fetch('/api/checkout-url', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ productId })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.checkoutUrl;
      } catch (error) {
        console.error('Failed to create checkout URL:', error);
        return null;
      }
    }

    setupCheckoutEvents(checkout, triggerId) {
      // Ensure any Polar internal loader/backdrop is cleared on load/close
      const cleanupPolarLoader = (attempt = 0) => {
        // Defensive: hide our overlay if still visible
        if (triggerId) this.hideLoading(triggerId);
        // Try to remove any lingering Polar overlay/spinner elements
        try {
          // 1) Explicit spinner element reported: .polar-loader-spinner
          const spinner = document.querySelector('.polar-loader-spinner');
          if (spinner) {
            // Hide and remove spinner
            if (spinner instanceof HTMLElement) spinner.style.display = 'none';
            const container = spinner.parentElement;
            if (container && container instanceof HTMLElement) container.style.display = 'none';
            try { spinner.remove(); } catch (_) {}
            try { if (container && container.parentElement) container.remove(); } catch (_) {}
          }

          // 2) Generic overlays known from Polar embed
          const overlays = document.querySelectorAll('[data-polar-embed-overlay], .polar-embed-overlay, .polar-embed-loader');
          overlays.forEach((el) => { if (el instanceof HTMLElement) el.style.display = 'none'; });
        } catch (_) {}

        // Retry a few times in case Polar appends spinner slightly after events
        if (attempt < 5) {
          setTimeout(() => cleanupPolarLoader(attempt + 1), 200);
        }
      };

      // Handle successful checkout
      checkout.addEventListener('success', (event) => {
        console.log('Checkout successful:', event.detail);
        cleanupPolarLoader();

        // Track analytics
        if (typeof gtag !== 'undefined') {
          gtag('event', 'purchase', {
            event_category: 'ecommerce',
            event_label: 'embed_checkout'
          });
        }

        // Redirect to success page with checkout ID
        if (event.detail.checkoutId) {
          window.location.href = `/success?checkout_id=${event.detail.checkoutId}`;
        } else {
          window.location.href = '/success';
        }
      });

      // Handle checkout close
      checkout.addEventListener('close', (event) => {
        console.log('Checkout closed');
        cleanupPolarLoader();
      });

      // Handle checkout loaded
      checkout.addEventListener('loaded', (event) => {
        console.log('Checkout loaded');
        cleanupPolarLoader();
      });

      // Handle checkout confirmed (payment processing)
      checkout.addEventListener('confirmed', (event) => {
        console.log('Payment confirmed, processing...');
      });
    }

    showLoading(triggerId) {
      const loadingEl = document.getElementById(`${triggerId}-loading`);
      if (loadingEl) {
        loadingEl.classList.remove('hidden');
      }
    }

    hideLoading(triggerId) {
      const loadingEl = document.getElementById(`${triggerId}-loading`);
      if (loadingEl) {
        loadingEl.classList.add('hidden');
      }
    }

    showError(message) {
      // Simple error notification
      const errorDiv = document.createElement('div');
      errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
      errorDiv.textContent = message;
      
      document.body.appendChild(errorDiv);
      
      // Auto remove after 5 seconds
      setTimeout(() => {
        if (errorDiv.parentNode) {
          errorDiv.parentNode.removeChild(errorDiv);
        }
      }, 5000);
    }
  }

  // Initialize when script loads
  new EmbedCheckoutHandler();
</script>
