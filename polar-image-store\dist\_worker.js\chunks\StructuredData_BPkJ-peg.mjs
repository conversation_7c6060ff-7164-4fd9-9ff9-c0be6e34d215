globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead,b as addAttribute,s as spreadAttributes,e as renderScript,f as renderSlot,u as unescapeHTML}from"./astro/server_BgKLHZ62.mjs";import{$ as $$Image}from"./_astro_assets_BJ9yS0_G.mjs";import{getResponsiveImageUrls,getOptimizedImageUrl,generateSizesAttribute}from"./imageOptimization_Dac7PwDt.mjs";const $$Astro$2=createAstro("https://infpik.store"),$$OptimizedImage=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro$2,t,r);a.self=$$OptimizedImage;const{src:i,alt:s,width:o=800,height:n=600,quality:c=85,format:d="auto",fit:p="scale-down",loading:l="lazy",fetchpriority:m="auto",class:u="",style:g="",responsive:h=!0,sizes:f=[320,640,960,1280,1920],densities:$=[1,2],preset:b,...y}=a.props;let A,_;if(b){const{ImagePresets:e}=await import("./imageOptimization_Dac7PwDt.mjs");A=e[b](i)}else if(h){const e=getResponsiveImageUrls(i,{sizes:f,densities:$,width:o,height:n,quality:c,format:d,fit:p});A=e.src,_=e.srcset}else{A=getOptimizedImageUrl(i,{width:o,height:n,quality:c,format:d,fit:p})}const v=h?generateSizesAttribute():void 0,k=i.startsWith("/")||i.includes("placeholder");return renderTemplate`${k?renderTemplate`<!-- Use Astro's built-in Image component for local images -->
  ${renderComponent(e,"Image",$$Image,{src:i,alt:s,width:o,height:n,loading:l,fetchpriority:m,class:u,style:g,...y})}`:renderTemplate`<!-- Use optimized external image with Cloudflare Image Transform -->
  ${maybeRenderHead()}<img${addAttribute(A,"src")}${addAttribute(_,"srcset")}${addAttribute(v,"sizes")}${addAttribute(s,"alt")}${addAttribute(o,"width")}${addAttribute(n,"height")}${addAttribute(l,"loading")}${addAttribute(m,"fetchpriority")}${addAttribute(u,"class")}${addAttribute(g,"style")}${spreadAttributes(y)}>`}`}),"D:/code/image/polar-image-store/src/components/OptimizedImage.astro",void 0);var _a$1,__freeze$1=Object.freeze,__defProp$1=Object.defineProperty,__template$1=(e,t)=>__freeze$1(__defProp$1(e,"raw",{value:__freeze$1(e.slice())}));const $$Astro$1=createAstro("https://infpik.store"),$$EmbedCheckout=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro$1,t,r);a.self=$$EmbedCheckout;const{productId:i,variant:s="button",className:o="",theme:n="light",size:c="md"}=a.props,d=`checkout-trigger-${i}-${Math.random().toString(36).substr(2,9)}`,p=`\n  inline-flex items-center justify-center gap-2 \n  bg-accent-600 text-white rounded-full font-semibold \n  transition-all duration-300 \n  hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5\n  ${{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[c]}\n  ${o}\n`,l=`\n  inline-flex items-center gap-2 \n  text-accent-600 hover:text-accent-700 font-medium \n  transition-colors duration-200\n  ${o}\n`;return renderTemplate(_a$1||(_a$1=__template$1(["","\x3c!-- Loading state overlay --\x3e<div",' class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"> <div class="bg-white rounded-2xl p-8 flex flex-col items-center gap-4 shadow-2xl"> <img src="/logo.svg" alt="InfPik" class="w-16 h-16 animate-pulse"> <span class="text-gray-700 font-medium">Loading checkout...</span> </div> </div> \x3c!-- Include the Polar embed script --\x3e <script src="https://cdn.jsdelivr.net/npm/@polar-sh/checkout@0.1/dist/embed.global.js" defer><\/script> ',""])),"button"===s?renderTemplate`${maybeRenderHead()}<button${addAttribute(d,"id")}${addAttribute(p,"class")} data-polar-checkout${addAttribute(n,"data-polar-checkout-theme")}${addAttribute(i,"data-product-id")}><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path></svg>${renderSlot(e,r.default,renderTemplate`Buy Now`)}</button>`:renderTemplate`<a${addAttribute(d,"id")} href="#"${addAttribute(l,"class")} data-polar-checkout${addAttribute(n,"data-polar-checkout-theme")}${addAttribute(i,"data-product-id")}><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path></svg>${renderSlot(e,r.default,renderTemplate`Buy Now`)}</a>`,addAttribute(`${d}-loading`,"id"),renderScript(e,"D:/code/image/polar-image-store/src/components/EmbedCheckout.astro?astro&type=script&index=0&lang.ts"))}),"D:/code/image/polar-image-store/src/components/EmbedCheckout.astro",void 0);var _a,__freeze=Object.freeze,__defProp=Object.defineProperty,__template=(e,t)=>__freeze(__defProp(e,"raw",{value:__freeze(e.slice())}));const $$Astro=createAstro("https://infpik.store"),$$StructuredData=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$StructuredData;const{type:i,data:s}=a.props;const o=function(e,t){const r={"@context":"https://schema.org","@type":e};switch(e){case"Product":return{...r,name:t.name,description:t.description,image:t.images||[],sku:t.id,brand:{"@type":"Brand",name:"InfPik"},offers:{"@type":"Offer",price:t.price,priceCurrency:t.currency||"USD",availability:t.isAvailable?"https://schema.org/InStock":"https://schema.org/OutOfStock",seller:{"@type":"Organization",name:"InfPik",url:"https://infpik.store"},url:t.url},category:"Digital Icons",productID:t.id,...t.aggregateRating&&{aggregateRating:{"@type":"AggregateRating",ratingValue:t.aggregateRating.ratingValue,reviewCount:t.aggregateRating.reviewCount}}};case"Organization":return{...r,name:"InfPik",url:"https://infpik.store",logo:"https://infpik.store/favicon.svg",description:"Premium 3D Premium Icons Store",sameAs:["https://twitter.com/polarimagestore","https://facebook.com/polarimagestore"],contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>"}};case"WebSite":return{...r,name:"InfPik",url:"https://infpik.store",description:"3D Premium Icons Store",publisher:{"@type":"Organization",name:"InfPik"},potentialAction:{"@type":"SearchAction",target:"https://infpik.store/products?search={search_term_string}","query-input":"required name=search_term_string"}};case"BreadcrumbList":return{...r,itemListElement:t.items.map(((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url})))};case"FAQPage":return{...r,mainEntity:t.faqs.map((e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}})))};case"Article":return{...r,headline:t.title,author:{"@type":"Person",name:t.author},datePublished:t.publishDate,dateModified:t.modifiedDate,publisher:{"@type":"Organization",name:"InfPik"},image:t.images?.map((e=>({"@type":"ImageObject",url:e,width:"1200",height:"900",caption:t.title})))||[]};default:return r}}(i,s);return renderTemplate(_a||(_a=__template(['<script type="application/ld+json">',"<\/script>"])),unescapeHTML(JSON.stringify(o)))}),"D:/code/image/polar-image-store/src/components/StructuredData.astro",void 0);export{$$OptimizedImage as $,$$EmbedCheckout as a,$$StructuredData as b};