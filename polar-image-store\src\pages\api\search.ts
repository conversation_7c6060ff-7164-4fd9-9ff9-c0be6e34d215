import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct } from '../../utils/polar';
import type { LocalProduct } from '../../types/polar';

export const prerender = false;

export const GET: APIRoute = async ({ url, locals }) => {
  try {
    const query = url.searchParams.get('q');

    // Get runtime environment from Cloudflare context
    const env = locals?.runtime?.env;
    const polar = createPolarClient(env);
    const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    const products = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // If no query, return popular/featured products
    if (!query || query.trim().length < 2) {
      const featuredProducts = products
        .slice(0, 10)
        .map(product => ({
          id: product.id,
          name: product.name,
          description: product.description,
          slug: product.slug,
          image: product.images[0] || '',
          price: product.price,
          currency: product.currency,
          url: `/products/${product.slug}`
        }));

      return new Response(
        JSON.stringify({
          results: featuredProducts,
          type: 'products'
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
          }
        }
      );
    }

    // Search for products by name and description
    const searchTerm = query.toLowerCase().trim();

    // Filter products based on search query
    const matchingProducts = products.filter(product => {
      const nameMatch = product.name.toLowerCase().includes(searchTerm);
      const descriptionMatch = product.description.toLowerCase().includes(searchTerm);

      return nameMatch || descriptionMatch;
    });

    // Create product results
    const productResults = matchingProducts.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      slug: product.slug,
      image: product.images[0] || '',
      price: product.price,
      currency: product.currency,
      url: `/products/${product.slug}`
    }));

    // Sort by relevance (exact name matches first, then partial matches)
    const sortedResults = productResults.sort((a, b) => {
      const aNameExact = a.name.toLowerCase() === searchTerm;
      const bNameExact = b.name.toLowerCase() === searchTerm;
      const aNameStart = a.name.toLowerCase().startsWith(searchTerm);
      const bNameStart = b.name.toLowerCase().startsWith(searchTerm);

      if (aNameExact && !bNameExact) return -1;
      if (!aNameExact && bNameExact) return 1;
      if (aNameStart && !bNameStart) return -1;
      if (!aNameStart && bNameStart) return 1;

      return a.name.localeCompare(b.name);
    });

    return new Response(
      JSON.stringify({
        results: sortedResults.slice(0, 10),
        type: 'products',
        total: sortedResults.length,
        query: query
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60' // Cache for 1 minute
        }
      }
    );

  } catch (error) {
    console.error('Search API error:', error);
    return new Response(
      JSON.stringify({ error: 'Search failed' }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};
